/* Interactive Anatomy Styles */
* { margin: 0; padding: 0; box-sizing: border-box; }
body { font-family: Arial, sans-serif; background: #222; color: #fff; overflow: hidden; }

#stage {
  position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
  width: 60vw; max-width: 800px;
  aspect-ratio: 600/853; /* Match SVG viewBox ratio exactly */
}

.layer { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

#body-layer {
  transition: all 0.8s ease;
  filter: blur(0px);
}
#body-layer.blurred {
  transform: translateX(-40%) scale(0.8);
  filter: blur(3px);
  opacity: 0.5;
  transition: all 0.8s ease; /* No delay when going to blurred */
}
#body-layer.restoring {
  transition: all 0.8s ease 0.25s; /* 0.25s delay when restoring */
}
#body-layer img { width: 100%; height: 100%; object-fit: cover; }

.detail-layer {
  opacity: 0;
  transform: translateX(0%) scale(0.8);
  transition: all 0.8s ease; /* No delay by default */
  z-index: 10;
}
.detail-layer.active {
  opacity: 1;
  transform: translateX(0%) scale(1);
  transition: all 0.8s ease 0.25s; /* 0.25s delay when scaling in */
  cursor: pointer; /* Indicate clickable */
}
.detail-layer img { width: 100%; height: 100%; object-fit: cover; }

/* Zoom states */
.detail-layer.zoomed {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 100;
  background: rgba(0,0,0,0.95);
  display: flex;
  align-items: center;
  justify-content: center;
  transform: none !important; /* Completely reset transforms */
  transition: all 0.5s ease;
  cursor: default;
}

.detail-layer.zoomed img {
  max-width: 95vw;
  max-height: 95vh;
  object-fit: contain;
  transition: all 0.5s ease;
  transform: none !important; /* Reset any image transforms */
  pointer-events: none; /* Prevent image from blocking background clicks */
}

/* Back button */
.back-button {
  position: fixed;
  top: 2rem;
  left: 2rem;
  width: 50px;
  height: 50px;
  background: rgba(255,255,255,0.9);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  z-index: 101;
  display: none;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #333;
  transition: all 0.3s ease;
}

.back-button:hover {
  background: rgba(255,255,255,1);
  transform: scale(1.1);
}

.back-button.visible {
  display: flex;
}

/* Text overlay - separate layer for zoomed text */
.text-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 100;
  background: rgba(0,0,0,0.95);
  display: none;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem;
  overflow-y: auto;
  cursor: default;
}

.text-overlay.visible {
  display: flex;
}

.text-overlay h2 {
  font-size: 3rem;
  margin-bottom: 2rem;
  color: #fff;
  text-transform: capitalize;
}

.text-overlay p {
  font-size: 1.4rem;
  line-height: 1.8;
  max-width: 80%;
  text-align: center;
  color: #ccc;
}

/* Mobile text overlay improvements */
@media (max-width: 768px) {
  .text-overlay {
    padding: 2rem 1rem; /* Reduce padding on mobile */
    overflow-y: auto; /* Ensure scrolling works */
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
  }

  .text-overlay h2 {
    font-size: 2rem; /* Smaller title on mobile */
    margin-bottom: 1rem;
  }

  .text-overlay p {
    font-size: 1rem; /* Smaller text on mobile */
    line-height: 1.6;
    max-width: 95%; /* Use more screen width */
    margin-bottom: 2rem; /* Add bottom margin for scrolling */
  }
}

.hotspot-svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 15;
  pointer-events: none;
  transition: all 0.8s ease;
}
.hotspot-svg.blurred {
  transform: translateX(-40%) scale(0.8);
  opacity: 0;
  transition: all 0.8s ease; /* No delay when going to blurred */
}
.hotspot-svg.restoring {
  transition: all 0.8s ease 0.25s; /* 0.25s delay when restoring */
}

.hotspot-path {
  fill: transparent;
  stroke: #fff;
  stroke-width: 2;
  transition: all 0.3s;
  opacity: 0.7;
  cursor: pointer;
  pointer-events: all;
}
.hotspot-path:hover {
  fill: rgba(255,255,255,0.2);
  stroke-width: 3;
  opacity: 1;
}

#text-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 30%;
  max-width: 350px;
  min-width: 280px;
  transform: translate(150%, -50%) scale(.8); /* Start from far right, outside screen */
  opacity: 0;
  transition: all 0.8s ease; /* No delay by default */
  background: rgba(0,0,0,0.8);
  padding: 2rem;
  border-radius: 10px;
  border: 1px solid #555;
  z-index: 5; /* Lower than SVG buttons (z-index: 10) */
  text-align: center; /* Center align text content */
}
#text-panel.active {
  transform: translate(70%, -50%) scale(1); /* Position between body and right edge */
  opacity: 1;
  transition: all 0.8s ease 0.25s; /* 0.25s delay when scaling in */
  cursor: pointer; /* Indicate clickable */
}
#text-panel h2 {
  margin-bottom: 1rem;
  color: #fff;
  text-transform: capitalize;
  font-size: 1.5rem;
}
#text-panel p {
  line-height: 1.6;
  color: #ccc;
  font-size: 0.9rem;
}

/* Responsive layout */
@media (max-width: 1024px) {
  #stage {
    width: 70vw;
  }
  #text-panel {
    width: 35%;
  }
  #text-panel.active {
    transform: translate(60%, -50%) scale(1); /* Closer to center on medium screens */
  }
}

/* Square-ish screens - push text towards center */
@media (max-aspect-ratio: 4/3) {
  #text-panel.active {
    transform: translate(45%, -50%) scale(1); /* Push towards center when screen is square-ish */
  }
}

/* Very square screens - push even more towards center */
@media (max-aspect-ratio: 5/4) {
  #text-panel.active {
    transform: translate(35%, -50%) scale(1); /* Even closer to center on very square screens */
  }
}

@media (max-width: 768px) {
  #stage {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%); /* Restore original centering */
    width: 95vw;
    height: 90vh;
  }

  /* Body and SVG - back to using .layer class like desktop */
  #body-layer, .hotspot-svg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  #body-layer img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .hotspot-svg svg {
    width: 100%; /* Match body image exactly */
    height: 100%; /* Match body image exactly */
    object-fit: contain;
  }

  /* Detail images - separate container for top half */
  .detail-layer {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 50vh;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
    padding: 1vh; /* Small padding to prevent edge touching */
  }

  .detail-layer img {
    width: 95vw; /* Force full window width */
    height: auto; /* Let height scale proportionally */
    max-height: none; /* Remove height constraint */
    object-fit: cover; /* Fill the width completely */
  }

  #body-layer.blurred {
    transform: translateX(-30%) scale(0.7); /* Less movement and scaling on mobile */
  }
  .hotspot-svg.blurred {
    transform: translateX(-30%) scale(0.7); /* Match body movement exactly */
  }

  .detail-layer.active {
    transform: translateX(0%) scale(1); /* Center and full scale */
  }

  #text-panel {
    position: fixed;
    top: 50vh; /* Start at bottom half */
    bottom: 2vh; /* Small margin at bottom */
    left: 3%; /* Close to edges */
    right: 3%; /* Close to edges */
    width: auto;
    max-width: none;
    height: auto; /* Fill bottom half */
    transform: translateY(100%) scale(.8);
    display: flex;
    flex-direction: column;
    overflow-y: auto; /* Scrollable */
    padding: 2rem;
    font-size: 1.2rem; /* Even larger text */
  }
  #text-panel.active {
    transform: translateY(0%) scale(1);
  }
  #text-panel p {
    overflow-y: auto;
    flex: 1;
    margin: 0;
    padding-right: 0.5rem; /* Space for scrollbar */
  }
}
