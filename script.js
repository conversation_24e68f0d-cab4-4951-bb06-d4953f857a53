// Interactive Anatomy Script
// This file contains all the functionality - students should not edit this file

// Initialize the interface based on configuration
function initializeInterface() {
  // Set body image
  const bodyImg = document.querySelector('#body-layer img');
  bodyImg.src = CONFIG.body.src;
  
  // Apply custom fonts if specified
  if (CONFIG.text.font) {
    const link = document.createElement('link');
    link.href = CONFIG.text.font;
    link.rel = 'stylesheet';
    document.head.appendChild(link);
  }
  
  // Apply button styles
  applyButtonStyles();
  
  // Apply text panel styles
  applyTextStyles();
}

function applyButtonStyles() {
  Object.entries(CONFIG.buttons).forEach(([buttonId, config]) => {
    const button = document.querySelector(`[data-button="${buttonId}"]`);
    if (button) {
      const path = button.querySelector('.hotspot-path');
      if (path) {
        path.style.stroke = config.lineColor;
        path.style.strokeWidth = config.lineThickness;
        path.style.fill = 'transparent';
        
        // Set hover styles
        path.addEventListener('mouseenter', () => {
          path.style.fill = config.fillColor;
          path.style.fillOpacity = config.fillOpacity;
        });
        path.addEventListener('mouseleave', () => {
          path.style.fill = 'transparent';
        });
      }
    }
  });
}

function applyTextStyles() {
  const textPanel = document.getElementById('text-panel');
  const textOverlay = document.getElementById('text-overlay');
  
  // Apply global text styles
  [textPanel, textOverlay].forEach(panel => {
    if (panel) {
      panel.style.fontSize = CONFIG.text.textSize;
      panel.style.color = CONFIG.text.textColor;
      panel.style.backgroundColor = `${CONFIG.text.backgroundColor}${Math.round(CONFIG.text.backgroundOpacity * 255).toString(16).padStart(2, '0')}`;
      
      if (CONFIG.text.background) {
        panel.style.backgroundImage = `url(${CONFIG.text.background})`;
      }
      
      if (CONFIG.text.font) {
        panel.style.fontFamily = 'inherit'; // Will use the loaded font
      }
    }
  });
}

let isDetailView = false;
let isZoomedView = false;
let isTextZoomed = false;
let currentDetail = null;

const bodyLayer = document.getElementById('body-layer');
const textPanel = document.getElementById('text-panel');
const title = document.getElementById('story-title');
const text = document.getElementById('story-text');
const backButton = document.getElementById('back-button');
const textOverlay = document.getElementById('text-overlay');
const overlayTitle = document.getElementById('overlay-title');
const overlayText = document.getElementById('overlay-text');

// Event listeners
document.querySelectorAll('.hotspot-path').forEach(path => {
  path.addEventListener('click', (e) => {
    e.stopPropagation();
    const svg = path.closest('.hotspot-svg');
    const buttonId = svg.getAttribute('data-button');
    const bodypartId = CONFIG.connections[buttonId];
    if (bodypartId) {
      showDetailView(buttonId, bodypartId);
    }
  });
});

// Detail image click to zoom
document.querySelectorAll('.detail-layer').forEach(layer => {
  layer.addEventListener('click', (e) => {
    e.stopPropagation();
    if (!isZoomedView && isDetailView) {
      showZoomView(layer);
    }
  });
});

// Text panel click to zoom
textPanel.addEventListener('click', (e) => {
  e.stopPropagation();
  if (!isTextZoomed && isDetailView) {
    showTextZoom();
  }
});

// Back button click
backButton.addEventListener('click', (e) => {
  e.stopPropagation();
  if (isZoomedView) {
    hideZoomView();
  } else if (isTextZoomed) {
    hideTextZoom();
  } else if (isDetailView) {
    hideDetailView();
  }
});

function showDetailView(buttonId, bodypartId) {
  if (isDetailView) return;

  isDetailView = true;
  currentDetail = bodypartId;
  
  const bodypartConfig = CONFIG.bodyparts[bodypartId];
  if (!bodypartConfig) return;

  // Blur and move body to left with 80% scale and 50% opacity
  bodyLayer.classList.add('blurred');

  // Hide all SVG hotspots (move and fade them with body)
  document.querySelectorAll('.hotspot-svg').forEach(svg => {
    svg.classList.add('blurred');
  });

  // Show corresponding detail image in center
  const detailElement = document.getElementById(bodypartId + '-detail');
  if (detailElement) {
    const img = detailElement.querySelector('img');
    if (img) {
      img.src = bodypartConfig.src;
      img.style.transform = `scale(${bodypartConfig.scale})`;
    }
    detailElement.classList.add('active');
  }

  // Show text panel on right side with configured styling
  title.textContent = bodypartConfig.title || bodypartId.replace('bodypart-', 'Body Part ');
  text.textContent = bodypartConfig.text;

  textPanel.classList.add('active');

  // Show back button in detail view
  backButton.classList.add('visible');
}

function hideDetailView() {
  if (!isDetailView) return;

  isDetailView = false;
  currentDetail = null;

  // Add restoring class for delayed transition
  bodyLayer.classList.add('restoring');
  document.querySelectorAll('.hotspot-svg').forEach(svg => {
    svg.classList.add('restoring');
  });

  // Remove blurred state
  bodyLayer.classList.remove('blurred');
  document.querySelectorAll('.hotspot-svg').forEach(svg => {
    svg.classList.remove('blurred');
  });

  // Hide detail image and text panel
  document.querySelectorAll('.detail-layer').forEach(layer => {
    layer.classList.remove('active');
  });
  textPanel.classList.remove('active');

  // Hide back button
  backButton.classList.remove('visible');

  // Clean up restoring class after transition
  setTimeout(() => {
    bodyLayer.classList.remove('restoring');
    document.querySelectorAll('.hotspot-svg').forEach(svg => {
      svg.classList.remove('restoring');
    });
  }, 1050); // Slightly longer than transition duration
}

function showZoomView(detailElement) {
  if (isZoomedView) return;
  
  isZoomedView = true;
  detailElement.classList.add('zoomed');
  
  // Show back button
  backButton.classList.add('visible');
}

function hideZoomView() {
  if (!isZoomedView) return;
  
  isZoomedView = false;
  
  // Hide back button
  backButton.classList.remove('visible');
  
  // Remove zoom from all detail layers
  document.querySelectorAll('.detail-layer').forEach(layer => {
    layer.classList.remove('zoomed');
  });
}

function showTextZoom() {
  if (isTextZoomed || isZoomedView) return;

  isTextZoomed = true;

  // Copy content to overlay and show it
  overlayTitle.textContent = title.textContent;
  overlayText.textContent = text.textContent;

  // Apply scaled text styling to overlay
  overlayTitle.style.fontSize = `calc(${CONFIG.scaledText.textSize} * 2.5)`;
  overlayText.style.fontSize = `calc(${CONFIG.scaledText.textSize} * 1.2)`;
  overlayTitle.style.color = CONFIG.scaledText.textColor;
  overlayText.style.color = CONFIG.scaledText.textColor;
  textOverlay.style.backgroundColor = `${CONFIG.scaledText.backgroundColor}${Math.round(CONFIG.scaledText.backgroundOpacity * 255).toString(16).padStart(2, '0')}`;

  if (CONFIG.scaledText.background) {
    textOverlay.style.backgroundImage = `url(${CONFIG.scaledText.background})`;
  }

  if (CONFIG.text.font) {
    textOverlay.style.fontFamily = 'inherit'; // Will use the loaded font
  }

  textOverlay.classList.add('visible');

  // Show back button
  backButton.classList.add('visible');
}

function hideTextZoom() {
  if (!isTextZoomed) return;
  
  isTextZoomed = false;
  
  // Hide back button
  backButton.classList.remove('visible');
  
  // Hide text overlay
  textOverlay.classList.remove('visible');
}

// Click anywhere else to close
document.addEventListener('click', (e) => {
  // Don't close if clicking on hotspot paths or back button
  if (e.target.closest('.hotspot-path') || 
      e.target.closest('.back-button')) {
    return;
  }
  
  // Special handling for zoomed states
  if (isZoomedView) {
    // Always exit zoom view when clicking anywhere (except back button which is handled above)
    hideZoomView();
    return;
  }
  
  if (isTextZoomed) {
    // In text zoom, only prevent closing if clicking on text content in overlay
    if (e.target.closest('#text-overlay h2') || e.target.closest('#text-overlay p')) {
      return;
    }
    hideTextZoom();
    return;
  }
  
  // Don't close if clicking on detail images when NOT zoomed
  if (e.target.closest('.detail-layer') && !isZoomedView) {
    return;
  }
  
  // Don't close if clicking on text panel when NOT text-zoomed
  if (e.target.closest('#text-panel') && !isTextZoomed) {
    return;
  }
  
  if (isDetailView) {
    hideDetailView();
  }
});

// Keyboard shortcuts
document.addEventListener('keydown', (e) => {
  if (e.key === 'Escape') {
    if (isZoomedView) {
      hideZoomView();
    } else if (isTextZoomed) {
      hideTextZoom();
    } else if (isDetailView) {
      hideDetailView();
    }
  }
});

// Initialize the interface when page loads
document.addEventListener('DOMContentLoaded', initializeInterface);
