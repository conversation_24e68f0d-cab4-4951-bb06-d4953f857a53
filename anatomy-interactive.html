<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Anatomy Stories</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: Arial, sans-serif; background: #222; color: #fff; overflow: hidden; }

    #stage {
      position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
      width: 60vw; max-width: 800px; aspect-ratio: 3/4;
    }

    .layer { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

    #body-layer {
      transition: all 0.8s ease;
      filter: blur(0px);
    }
    #body-layer.blurred {
      transform: translateX(-33%) scale(0.8);
      filter: blur(3px);
      opacity: 0.5;
    }
    #body-layer img { width: 100%; height: 100%; object-fit: cover; }

    .detail-layer {
      opacity: 0;
      transform: scale(0);
      transition: all 0.8s ease;
      z-index: 10;
    }
    .detail-layer.active {
      opacity: 1;
      transform: scale(0.8);
    }
    .detail-layer img { width: 100%; height: 100%; object-fit: cover; }

    .hotspot-svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      cursor: pointer;
      z-index: 15;
    }

    .hotspot-path {
      fill: transparent;
      stroke: #fff;
      stroke-width: 2;
      transition: all 0.3s;
      opacity: 0.7;
    }
    .hotspot-svg:hover .hotspot-path {
      fill: rgba(255,255,255,0.2);
      stroke-width: 3;
      opacity: 1;
    }

    #text-panel {
      position: fixed;
      top: 50%;
      right: 5%;
      width: 300px;
      transform: translateY(-50%) scale(0);
      opacity: 0;
      transition: all 0.8s ease;
      background: rgba(0,0,0,0.8);
      padding: 2rem;
      border-radius: 10px;
      border: 1px solid #555;
      z-index: 20;
    }
    #text-panel.active {
      transform: translateY(-50%) scale(1);
      opacity: 1;
    }
    #text-panel h2 {
      margin-bottom: 1rem;
      color: #fff;
      text-transform: capitalize;
      font-size: 1.5rem;
    }
    #text-panel p {
      line-height: 1.6;
      color: #ccc;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <div id="stage">
    <!-- Main body layer -->
    <div id="body-layer" class="layer">
      <img src="png/body.png" alt="Body">
    </div>

    <!-- Detail layers (initially hidden) -->
    <div id="leg-detail" class="layer detail-layer">
      <img src="png/leg.png" alt="Leg Detail">
    </div>
    <div id="hand-detail" class="layer detail-layer">
      <img src="png/hand.png" alt="Hand Detail">
    </div>

    <!-- SVG overlay with precise hotspots -->
    <div class="layer">
      <!-- Hand SVG hotspot -->
      <svg id="hand-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-detail="hand" data-story="hand">
        <path class="hotspot-path" d="M374.21,212.19c2.36,9.92,12.26,18.04,12.26,18.04l24.17-9.02,31.66-17.53s2.38-11.91-6.13-20.77-10.55-7.49-10.55-7.49l-26.89,15.32-10.89,6.13-13.62-1.36s-1.7,9.53,0,16.68Z"/>
      </svg>

      <!-- Leg SVG hotspot -->
      <svg id="leg-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-detail="leg" data-story="knee">
        <path class="hotspot-path" d="M302.72,426.5s-1.36,25.35-2.72,41.69-6.13,36.09-4.43,46.3,7.49,8.17,10.55,9.87,34.04,1.7,37.79,0,18.38-48.29,18.38-48.29c0,0,13.62-46.32,13.62-49.57s-5.11-6.65-22.13-4.27-51.06,4.27-51.06,4.27Z"/>
      </svg>
    </div>
  </div>

  <!-- Text panel -->
  <div id="text-panel">
    <h2 id="story-title">Body Part</h2>
    <p id="story-text">Story text will appear here...</p>
  </div>

  <script>
    const stories = {
      heart: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo. Nemo enim ipsam voluptatem quia voluptas sit aspernatur aut odit aut fugit.",
      elbow: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Mauris vel sapien vel nulla tempus cursus. Vestibulum ante ipsum primis in faucibus orci luctus et ultrices posuere cubilia curae; Donec velit neque, auctor sit amet aliquam vel, ullamcorper sit amet ligula. Curabitur arcu erat, accumsan id imperdiet et, porttitor at sem. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo.",
      knee: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin eget tortor risus. Vestibulum ac diam sit amet quam vehicula elementum sed sit amet dui. Mauris blandit aliquet elit, eget tincidunt nibh pulvinar a. Curabitur non nulla sit amet nisl tempus convallis quis ac lectus. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque velit nisi, pretium ut lacinia in, elementum id enim.",
      hand: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."
    };

    let isDetailView = false;
    let currentDetail = null;

    const bodyLayer = document.getElementById('body-layer');
    const textPanel = document.getElementById('text-panel');
    const title = document.getElementById('story-title');
    const text = document.getElementById('story-text');

    document.querySelectorAll('.hotspot-svg').forEach(hotspot => {
      hotspot.addEventListener('click', (e) => {
        e.stopPropagation();
        const storyId = hotspot.getAttribute('data-story');
        const detailType = hotspot.getAttribute('data-detail');
        showDetailView(storyId, detailType);
      });
    });

    function showDetailView(id, detailType) {
      if (isDetailView) return;

      isDetailView = true;
      currentDetail = detailType;

      // Blur and move body to left with 80% scale and 50% opacity
      bodyLayer.classList.add('blurred');

      // Show corresponding detail image
      const detailElement = document.getElementById(detailType + '-detail');
      if (detailElement) {
        detailElement.classList.add('active');
      }

      // Show text panel on right side
      title.textContent = id.charAt(0).toUpperCase() + id.slice(1);
      text.textContent = stories[id] || "No story available.";
      textPanel.classList.add('active');
    }

    function hideDetailView() {
      if (!isDetailView) return;

      isDetailView = false;

      // Restore body layer
      bodyLayer.classList.remove('blurred');

      // Hide detail image
      if (currentDetail) {
        const detailElement = document.getElementById(currentDetail + '-detail');
        if (detailElement) {
          detailElement.classList.remove('active');
        }
      }

      // Hide text panel
      textPanel.classList.remove('active');

      currentDetail = null;
    }

    // Click anywhere else to close
    document.addEventListener('click', (e) => {
      if (isDetailView && !e.target.closest('.hotspot-svg')) {
        hideDetailView();
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') hideDetailView();
    });
  </script>
</body>
</html>
