<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Anatomy Stories</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: Arial, sans-serif; background: #222; color: #fff; overflow: hidden; }

    #stage {
      position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
      width: 60vw; max-width: 800px;
      aspect-ratio: 600/853; /* Match SVG viewBox ratio exactly */
    }

    .layer { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

    #body-layer {
      transition: all 0.8s ease;
      filter: blur(0px);
    }
    #body-layer.blurred {
      transform: translateX(-40%) scale(0.8);
      filter: blur(3px);
      opacity: 0.5;
      transition: all 0.8s ease; /* No delay when going to blurred */
    }
    #body-layer.restoring {
      transition: all 0.8s ease 0.25s; /* 0.25s delay when restoring */
    }
    #body-layer img { width: 100%; height: 100%; object-fit: cover; }

    .detail-layer {
      opacity: 0;
      transform: translateX(0%) scale(0.8);
      transition: all 0.8s ease; /* No delay by default */
      z-index: 10;
    }
    .detail-layer.active {
      opacity: 1;
      transform: translateX(0%) scale(1);
      transition: all 0.8s ease 0.25s; /* 0.25s delay when scaling in */
      cursor: pointer; /* Indicate clickable */
    }
    .detail-layer img { width: 100%; height: 100%; object-fit: cover; }

    /* Zoom states */
    .detail-layer.zoomed {
      position: fixed;
      top: 0;
      left: 0;
      width: 100vw;
      height: 100vh;
      z-index: 100;
      background: rgba(0,0,0,0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      transform: translate(-50%, -50%) scale(1) !important; /* Override any inherited transforms */
      top: 50% !important;
      left: 50% !important;
      transition: all 0.5s ease;
      cursor: default;
    }

    .detail-layer.zoomed img {
      max-width: 95vw;
      max-height: 95vh;
      object-fit: contain;
      transition: all 0.5s ease;
      transform: none; /* Reset any image transforms */
    }

    /* Back button */
    .back-button {
      position: fixed;
      top: 2rem;
      left: 2rem;
      width: 50px;
      height: 50px;
      background: rgba(255,255,255,0.9);
      border: none;
      border-radius: 50%;
      cursor: pointer;
      z-index: 101;
      display: none;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      color: #333;
      transition: all 0.3s ease;
    }

    .back-button:hover {
      background: rgba(255,255,255,1);
      transform: scale(1.1);
    }

    .back-button.visible {
      display: flex;
    }

    .hotspot-svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 15;
      pointer-events: none;
      transition: all 0.8s ease;
    }
    .hotspot-svg.blurred {
      transform: translateX(-40%) scale(0.8);
      opacity: 0;
      transition: all 0.8s ease; /* No delay when going to blurred */
    }
    .hotspot-svg.restoring {
      transition: all 0.8s ease 0.25s; /* 0.25s delay when restoring */
    }

    .hotspot-path {
      fill: transparent;
      stroke: #fff;
      stroke-width: 2;
      transition: all 0.3s;
      opacity: 0.7;
      cursor: pointer;
      pointer-events: all;
    }
    .hotspot-path:hover {
      fill: rgba(255,255,255,0.2);
      stroke-width: 3;
      opacity: 1;
    }

    #text-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      width: 30%;
      max-width: 350px;
      min-width: 280px;
      transform: translate(150%, -50%) scale(.8); /* Start from far right, outside screen */
      opacity: 0;
      transition: all 0.8s ease; /* No delay by default */
      background: rgba(0,0,0,0.8);
      padding: 2rem;
      border-radius: 10px;
      border: 1px solid #555;
      z-index: 5; /* Lower than SVG buttons (z-index: 10) */
      text-align: center; /* Center align text content */
    }
    #text-panel.active {
      transform: translate(70%, -50%) scale(1); /* Position between body and right edge */
      opacity: 1;
      transition: all 0.8s ease 0.25s; /* 0.25s delay when scaling in */
    }
    #text-panel h2 {
      margin-bottom: 1rem;
      color: #fff;
      text-transform: capitalize;
      font-size: 1.5rem;
    }
    #text-panel p {
      line-height: 1.6;
      color: #ccc;
      font-size: 0.9rem;
    }

    /* Responsive layout */
    @media (max-width: 1024px) {
      #stage {
        width: 70vw;
      }
      #text-panel {
        width: 35%;
      }
      #text-panel.active {
        transform: translate(60%, -50%) scale(1); /* Closer to center on medium screens */
      }
    }

    /* Square-ish screens - push text towards center */
    @media (max-aspect-ratio: 4/3) {
      #text-panel.active {
        transform: translate(45%, -50%) scale(1); /* Push towards center when screen is square-ish */
      }
    }

    /* Very square screens - push even more towards center */
    @media (max-aspect-ratio: 5/4) {
      #text-panel.active {
        transform: translate(35%, -50%) scale(1); /* Even closer to center on very square screens */
      }
    }

    @media (max-width: 768px) {
      #stage {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%); /* Restore original centering */
        width: 95vw;
        height: 90vh;
      }

      /* Body and SVG - back to using .layer class like desktop */
      #body-layer, .hotspot-svg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      #body-layer img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      .hotspot-svg svg {
        width: 100%; /* Match body image exactly */
        height: 100%; /* Match body image exactly */
        object-fit: contain;
      }

      /* Detail images - separate container for top half */
      .detail-layer {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 50vh;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        padding: 1vh; /* Small padding to prevent edge touching */
      }

      .detail-layer img {
        width: 95vw; /* Force full window width */
        height: auto; /* Let height scale proportionally */
        max-height: none; /* Remove height constraint */
        object-fit: cover; /* Fill the width completely */
      }

      #body-layer.blurred {
        transform: translateX(-30%) scale(0.7); /* Less movement and scaling on mobile */
      }
      .hotspot-svg.blurred {
        transform: translateX(-30%) scale(0.7); /* Match body movement exactly */
      }

      .detail-layer.active {
        transform: translateX(0%) scale(1); /* Center and full scale */
      }

      #text-panel {
        position: fixed;
        top: 50vh; /* Start at bottom half */
        bottom: 2vh; /* Small margin at bottom */
        left: 3%; /* Close to edges */
        right: 3%; /* Close to edges */
        width: auto;
        max-width: none;
        height: auto; /* Fill bottom half */
        transform: translateY(100%) scale(.8);
        display: flex;
        flex-direction: column;
        overflow-y: auto; /* Scrollable */
        padding: 2rem;
        font-size: 1.2rem; /* Even larger text */
      }
      #text-panel.active {
        transform: translateY(0%) scale(1);
      }
      #text-panel p {
        overflow-y: auto;
        flex: 1;
        margin: 0;
        padding-right: 0.5rem; /* Space for scrollbar */
      }
    }
  </style>
</head>
<body>
  <div id="stage">
    <!-- Main body layer -->
    <div id="body-layer" class="layer">
      <img src="png/body.png" alt="Body">
    </div>

    <!-- Detail layers (initially hidden) -->
    <div id="leg-detail" class="layer detail-layer">
      <img src="png/leg.png" alt="Leg Detail">
    </div>
    <div id="hand-detail" class="layer detail-layer">
      <img src="png/hand.png" alt="Hand Detail">
    </div>

    <!-- SVG overlay with precise hotspots -->
    <div class="layer">
      <!-- Hand SVG hotspot -->
      <svg id="hand-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-detail="hand" data-story="hand">
        <path class="hotspot-path" d="M374.21,212.19c2.36,9.92,12.26,18.04,12.26,18.04l24.17-9.02,31.66-17.53s2.38-11.91-6.13-20.77-10.55-7.49-10.55-7.49l-26.89,15.32-10.89,6.13-13.62-1.36s-1.7,9.53,0,16.68Z"/>
      </svg>

      <!-- Leg SVG hotspot -->
      <svg id="leg-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-detail="leg" data-story="knee">
        <path class="hotspot-path" d="M302.72,426.5s-1.36,25.35-2.72,41.69-6.13,36.09-4.43,46.3,7.49,8.17,10.55,9.87,34.04,1.7,37.79,0,18.38-48.29,18.38-48.29c0,0,13.62-46.32,13.62-49.57s-5.11-6.65-22.13-4.27-51.06,4.27-51.06,4.27Z"/>
      </svg>
    </div>
  </div>

  <!-- Text panel -->
  <div id="text-panel">
    <h2 id="story-title">Body Part</h2>
    <p id="story-text">Story text will appear here...</p>
  </div>

  <!-- Back button for zoom view -->
  <button class="back-button" id="back-button">←</button>

  <script>
    const stories = {
      knee: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin eget tortor risus. Vestibulum ac diam sit amet quam vehicula elementum sed sit amet dui. Mauris blandit aliquet elit, eget tincidunt nibh pulvinar a. Curabitur non nulla sit amet nisl tempus convallis quis ac lectus. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque velit nisi, pretium ut lacinia in, elementum id enim.",
      hand: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."
    };

    let isDetailView = false;
    let isZoomedView = false;
    let currentDetail = null;

    const bodyLayer = document.getElementById('body-layer');
    const textPanel = document.getElementById('text-panel');
    const title = document.getElementById('story-title');
    const text = document.getElementById('story-text');
    const backButton = document.getElementById('back-button');

    document.querySelectorAll('.hotspot-path').forEach(path => {
      path.addEventListener('click', (e) => {
        e.stopPropagation();
        const svg = path.closest('.hotspot-svg');
        const storyId = svg.getAttribute('data-story');
        const detailType = svg.getAttribute('data-detail');
        showDetailView(storyId, detailType);
      });
    });

    // Add click handlers for detail images to zoom
    document.querySelectorAll('.detail-layer').forEach(detail => {
      detail.addEventListener('click', (e) => {
        e.stopPropagation();
        if (isDetailView && !isZoomedView) {
          showZoomView();
        }
      });
    });

    // Back button handler
    backButton.addEventListener('click', (e) => {
      e.stopPropagation();
      if (isZoomedView) {
        hideZoomView();
      }
    });

    function showDetailView(id, detailType) {
      if (isDetailView) return;

      isDetailView = true;
      currentDetail = detailType;

      // Blur and move body to left with 80% scale and 50% opacity
      bodyLayer.classList.add('blurred');

      // Hide all SVG hotspots (move and fade them with body)
      document.querySelectorAll('.hotspot-svg').forEach(svg => {
        svg.classList.add('blurred');
      });

      // Show corresponding detail image in center
      const detailElement = document.getElementById(detailType + '-detail');
      if (detailElement) {
        detailElement.classList.add('active');
      }

      // Show text panel on right side
      title.textContent = id.charAt(0).toUpperCase() + id.slice(1);
      text.textContent = stories[id] || "No story available.";
      textPanel.classList.add('active');
    }

    function showZoomView() {
      if (!isDetailView || isZoomedView) return;

      isZoomedView = true;

      // Hide text panel
      textPanel.classList.remove('active');

      // Zoom the current detail image
      if (currentDetail) {
        const detailElement = document.getElementById(currentDetail + '-detail');
        if (detailElement) {
          detailElement.classList.add('zoomed');
        }
      }

      // Show back button
      backButton.classList.add('visible');
    }

    function hideZoomView() {
      if (!isZoomedView) return;

      isZoomedView = false;

      // Hide back button
      backButton.classList.remove('visible');

      // Remove zoom from detail image
      if (currentDetail) {
        const detailElement = document.getElementById(currentDetail + '-detail');
        if (detailElement) {
          detailElement.classList.remove('zoomed');
        }
      }

      // Show text panel again
      textPanel.classList.add('active');
    }

    function hideDetailView() {
      if (!isDetailView) return;

      // If we're in zoom view, exit that first
      if (isZoomedView) {
        hideZoomView();
      }

      isDetailView = false;

      // Hide detail image first (no delay)
      if (currentDetail) {
        const detailElement = document.getElementById(currentDetail + '-detail');
        if (detailElement) {
          detailElement.classList.remove('active');
          detailElement.classList.remove('zoomed'); // Make sure zoom is also removed
        }
      }

      // Hide text panel (no delay)
      textPanel.classList.remove('active');

      // Hide back button
      backButton.classList.remove('visible');

      // Add restoring class for delayed body restoration
      bodyLayer.classList.add('restoring');
      bodyLayer.classList.remove('blurred');

      // Add restoring class for delayed SVG restoration
      document.querySelectorAll('.hotspot-svg').forEach(svg => {
        svg.classList.add('restoring');
        svg.classList.remove('blurred');
      });

      // Remove restoring classes after animation completes
      setTimeout(() => {
        bodyLayer.classList.remove('restoring');
        document.querySelectorAll('.hotspot-svg').forEach(svg => {
          svg.classList.remove('restoring');
        });
      }, 1050); // 0.25s delay + 0.8s animation

      currentDetail = null;
    }

    // Click anywhere else to close
    document.addEventListener('click', (e) => {
      // Don't close if clicking on hotspot paths, detail images, or back button
      if (e.target.closest('.hotspot-path') ||
          e.target.closest('.detail-layer') ||
          e.target.closest('.back-button')) {
        return;
      }

      if (isZoomedView) {
        hideZoomView();
      } else if (isDetailView) {
        hideDetailView();
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') {
        if (isZoomedView) {
          hideZoomView();
        } else if (isDetailView) {
          hideDetailView();
        }
      }
    });
  </script>
</body>
</html>
