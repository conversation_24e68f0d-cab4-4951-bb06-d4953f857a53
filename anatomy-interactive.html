<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Anatomy Stories</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: Arial, sans-serif; background: #222; color: #fff; overflow: hidden; }

    #stage {
      position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
      width: 60vw; max-width: 800px;
      aspect-ratio: 600/853; /* Match SVG viewBox ratio exactly */
    }

    .layer { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

    #body-layer {
      transition: all 0.8s ease;
      filter: blur(0px);
    }
    #body-layer.blurred {
      transform: translateX(-40%) scale(0.8);
      filter: blur(3px);
      opacity: 0.5;
      transition: all 0.8s ease; /* No delay when going to blurred */
    }
    #body-layer.restoring {
      transition: all 0.8s ease 0.25s; /* 0.25s delay when restoring */
    }
    #body-layer img { width: 100%; height: 100%; object-fit: cover; }

    .detail-layer {
      opacity: 0;
      transform: translateX(0%) scale(0.8);
      transition: all 0.8s ease; /* No delay by default */
      z-index: 10;
    }
    .detail-layer.active {
      opacity: 1;
      transform: translateX(0%) scale(1);
      transition: all 0.8s ease 0.25s; /* 0.25s delay when scaling in */
    }
    .detail-layer img { width: 100%; height: 100%; object-fit: cover; }

    .hotspot-svg {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 15;
      pointer-events: none;
      transition: all 0.8s ease;
    }
    .hotspot-svg.blurred {
      transform: translateX(-40%) scale(0.8);
      opacity: 0;
      transition: all 0.8s ease; /* No delay when going to blurred */
    }
    .hotspot-svg.restoring {
      transition: all 0.8s ease 0.25s; /* 0.25s delay when restoring */
    }

    .hotspot-path {
      fill: transparent;
      stroke: #fff;
      stroke-width: 2;
      transition: all 0.3s;
      opacity: 0.7;
      cursor: pointer;
      pointer-events: all;
    }
    .hotspot-path:hover {
      fill: rgba(255,255,255,0.2);
      stroke-width: 3;
      opacity: 1;
    }

    #text-panel {
      position: fixed;
      top: 50%;
      left: 50%;
      width: 30%;
      max-width: 350px;
      min-width: 280px;
      transform: translate(0%, -50%) scale(.8);
      opacity: 0;
      transition: all 0.8s ease; /* No delay by default */
      background: rgba(0,0,0,0.8);
      padding: 2rem;
      border-radius: 10px;
      border: 1px solid #555;
      z-index: 20;
    }
    #text-panel.active {
      transform: translate(20%, -50%) scale(1); /* Move less to the right from center */
      opacity: 1;
      transition: all 0.8s ease 0.25s; /* 0.25s delay when scaling in */
    }
    #text-panel h2 {
      margin-bottom: 1rem;
      color: #fff;
      text-transform: capitalize;
      font-size: 1.5rem;
    }
    #text-panel p {
      line-height: 1.6;
      color: #ccc;
      font-size: 0.9rem;
    }

    /* Responsive layout */
    @media (max-width: 1024px) {
      #stage {
        width: 70vw;
      }
      #text-panel {
        width: 35%;
        right: 1%;
      }
    }

    @media (max-width: 768px) {
      #stage {
        width: 100vw;
        height: 65vh; /* Slightly larger for better body fit */
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        align-items: center;
        justify-content: center;
      }

      #body-layer, .detail-layer, .hotspot-svg {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      #body-layer img, .detail-layer img {
        max-width: 95%;
        max-height: 95%;
        object-fit: contain;
      }

      #body-layer.blurred {
        transform: translateX(-60%) scale(0.5); /* Move left like desktop */
      }
      .hotspot-svg.blurred {
        transform: translateX(-60%) scale(0.5); /* Match body movement */
      }

      .detail-layer.active {
        transform: translateX(0%) scale(1); /* Center and full scale */
      }

      #text-panel {
        position: fixed;
        top: 65vh; /* Start after body area */
        bottom: 5vh;
        left: 10%;
        right: 10%;
        width: auto;
        max-width: none;
        height: 25vh; /* Adjusted for new proportions */
        transform: translateY(100%) scale(.8);
        display: flex;
        flex-direction: column;
        overflow-y: auto; /* Add scrolling */
        padding: 1.5rem;
      }
      #text-panel.active {
        transform: translateY(0%) scale(1);
      }
      #text-panel p {
        overflow-y: auto;
        flex: 1;
        margin: 0;
        padding-right: 0.5rem; /* Space for scrollbar */
      }
    }
  </style>
</head>
<body>
  <div id="stage">
    <!-- Main body layer -->
    <div id="body-layer" class="layer">
      <img src="png/body.png" alt="Body">
    </div>

    <!-- Detail layers (initially hidden) -->
    <div id="leg-detail" class="layer detail-layer">
      <img src="png/leg.png" alt="Leg Detail">
    </div>
    <div id="hand-detail" class="layer detail-layer">
      <img src="png/hand.png" alt="Hand Detail">
    </div>

    <!-- SVG overlay with precise hotspots -->
    <div class="layer">
      <!-- Hand SVG hotspot -->
      <svg id="hand-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-detail="hand" data-story="hand">
        <path class="hotspot-path" d="M374.21,212.19c2.36,9.92,12.26,18.04,12.26,18.04l24.17-9.02,31.66-17.53s2.38-11.91-6.13-20.77-10.55-7.49-10.55-7.49l-26.89,15.32-10.89,6.13-13.62-1.36s-1.7,9.53,0,16.68Z"/>
      </svg>

      <!-- Leg SVG hotspot -->
      <svg id="leg-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-detail="leg" data-story="knee">
        <path class="hotspot-path" d="M302.72,426.5s-1.36,25.35-2.72,41.69-6.13,36.09-4.43,46.3,7.49,8.17,10.55,9.87,34.04,1.7,37.79,0,18.38-48.29,18.38-48.29c0,0,13.62-46.32,13.62-49.57s-5.11-6.65-22.13-4.27-51.06,4.27-51.06,4.27Z"/>
      </svg>
    </div>
  </div>

  <!-- Text panel -->
  <div id="text-panel">
    <h2 id="story-title">Body Part</h2>
    <p id="story-text">Story text will appear here...</p>
  </div>

  <script>
    const stories = {
      knee: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin eget tortor risus. Vestibulum ac diam sit amet quam vehicula elementum sed sit amet dui. Mauris blandit aliquet elit, eget tincidunt nibh pulvinar a. Curabitur non nulla sit amet nisl tempus convallis quis ac lectus. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque velit nisi, pretium ut lacinia in, elementum id enim.",
      hand: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum. Sed ut perspiciatis unde omnis iste natus error sit voluptatem accusantium doloremque laudantium, totam rem aperiam, eaque ipsa quae ab illo inventore veritatis et quasi architecto beatae vitae dicta sunt explicabo."
    };

    let isDetailView = false;
    let currentDetail = null;

    const bodyLayer = document.getElementById('body-layer');
    const textPanel = document.getElementById('text-panel');
    const title = document.getElementById('story-title');
    const text = document.getElementById('story-text');

    document.querySelectorAll('.hotspot-path').forEach(path => {
      path.addEventListener('click', (e) => {
        e.stopPropagation();
        const svg = path.closest('.hotspot-svg');
        const storyId = svg.getAttribute('data-story');
        const detailType = svg.getAttribute('data-detail');
        showDetailView(storyId, detailType);
      });
    });

    function showDetailView(id, detailType) {
      if (isDetailView) return;

      isDetailView = true;
      currentDetail = detailType;

      // Blur and move body to left with 80% scale and 50% opacity
      bodyLayer.classList.add('blurred');

      // Hide all SVG hotspots (move and fade them with body)
      document.querySelectorAll('.hotspot-svg').forEach(svg => {
        svg.classList.add('blurred');
      });

      // Show corresponding detail image in center
      const detailElement = document.getElementById(detailType + '-detail');
      if (detailElement) {
        detailElement.classList.add('active');
      }

      // Show text panel on right side
      title.textContent = id.charAt(0).toUpperCase() + id.slice(1);
      text.textContent = stories[id] || "No story available.";
      textPanel.classList.add('active');
    }

    function hideDetailView() {
      if (!isDetailView) return;

      isDetailView = false;

      // Hide detail image first (no delay)
      if (currentDetail) {
        const detailElement = document.getElementById(currentDetail + '-detail');
        if (detailElement) {
          detailElement.classList.remove('active');
        }
      }

      // Hide text panel (no delay)
      textPanel.classList.remove('active');

      // Add restoring class for delayed body restoration
      bodyLayer.classList.add('restoring');
      bodyLayer.classList.remove('blurred');

      // Add restoring class for delayed SVG restoration
      document.querySelectorAll('.hotspot-svg').forEach(svg => {
        svg.classList.add('restoring');
        svg.classList.remove('blurred');
      });

      // Remove restoring classes after animation completes
      setTimeout(() => {
        bodyLayer.classList.remove('restoring');
        document.querySelectorAll('.hotspot-svg').forEach(svg => {
          svg.classList.remove('restoring');
        });
      }, 1050); // 0.25s delay + 0.8s animation

      currentDetail = null;
    }

    // Click anywhere else to close
    document.addEventListener('click', (e) => {
      if (isDetailView && !e.target.closest('.hotspot-path')) {
        hideDetailView();
      }
    });

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') hideDetailView();
    });
  </script>
</body>
</html>
