<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>Interactive Anatomy Stories</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <script src="https://aframe.io/releases/1.5.0/aframe.min.js"></script>
    <style>
      html, body {
        margin: 0;
        height: 100%;
        overflow: hidden;
        font-family: system-ui, -apple-system, sans-serif;
        background: #000;
      }

      /* Story panel on the left side */
      #storyPanel {
        position: absolute;
        left: 0;
        top: 0;
        width: 35%;
        height: 100%;
        background: linear-gradient(135deg, rgba(0,0,0,0.9), rgba(20,20,20,0.95));
        color: #fff;
        padding: 2rem;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        transform: translateX(-100%);
        transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 100;
        backdrop-filter: blur(10px);
        border-right: 1px solid rgba(255,255,255,0.1);
      }

      #storyPanel.active {
        transform: translateX(0);
      }

      #storyTitle {
        font-size: 2rem;
        font-weight: 300;
        margin-bottom: 1rem;
        color: #fff;
        text-transform: uppercase;
        letter-spacing: 2px;
      }

      #storyText {
        font-size: 1.1rem;
        line-height: 1.6;
        color: rgba(255,255,255,0.9);
        margin-bottom: 2rem;
      }

      #closeButton {
        background: rgba(255,255,255,0.1);
        border: 1px solid rgba(255,255,255,0.3);
        color: #fff;
        padding: 0.75rem 1.5rem;
        border-radius: 25px;
        cursor: pointer;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        align-self: flex-start;
      }

      #closeButton:hover {
        background: rgba(255,255,255,0.2);
        border-color: rgba(255,255,255,0.5);
      }

      /* Instructions overlay */
      #instructions {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: rgba(255,255,255,0.8);
        text-align: center;
        font-size: 1.2rem;
        pointer-events: none;
        transition: opacity 0.5s ease;
        z-index: 10;
        background: rgba(0,0,0,0.3);
        padding: 1rem 2rem;
        border-radius: 10px;
        backdrop-filter: blur(5px);
      }

      #instructions.hidden {
        opacity: 0;
      }

      /* Mobile optimizations */
      @media (max-width: 768px) {
        #storyPanel {
          width: 100%;
          padding: 1.5rem;
        }

        #storyTitle {
          font-size: 1.5rem;
        }

        #storyText {
          font-size: 1rem;
        }

        #instructions {
          font-size: 1rem;
          padding: 0.75rem 1.5rem;
        }
      }
    </style>
  </head>
  <body>
    <!-- Instructions overlay -->
    <div id="instructions">
      <p>👆 Click on any body part to explore its story</p>
    </div>

    <!-- Story panel -->
    <div id="storyPanel">
      <div id="storyTitle"></div>
      <div id="storyText"></div>
      <button id="closeButton">Close</button>
    </div>

    <a-scene
      embedded
      renderer="antialias: true; colorManagement: true; alpha: true"
      vr-mode-ui="enabled: false"
      webxr="enabled: false"
      background="color: #111"
    >
      <!-- Camera -->
      <a-entity id="cameraRig" position="0 0 4">
        <a-camera
          look-controls="enabled: true; touchEnabled: true"
          wasd-controls="enabled: false"
          cursor="rayOrigin: mouse; fuse: false"
          raycaster="objects: .clickable; far: 20; near: 0.1"
        ></a-camera>
      </a-entity>

      <!-- Assets -->
      <a-assets>
        <img id="img-body" src="body.png" alt="Complete body drawing" crossorigin="anonymous" />
        <img id="img-hand" src="hand.png" alt="Hand detail" crossorigin="anonymous" />
        <img id="img-foot" src="foot.png" alt="Foot detail" crossorigin="anonymous" />
        <img id="img-head" src="head.png" alt="Head detail" crossorigin="anonymous" />
      </a-assets>

      <!-- Main body image with clickable areas -->
      <a-plane id="mainBody"
        material="shader: flat; src: #img-body; side: double; transparent: true; alphaTest: 0.1"
        position="0 0 -2"
        scale="2 2 1"
        rotation="0 0 0"
      ></a-plane>

      <!-- Invisible clickable areas overlaid on body parts -->
      <a-box id="headArea" class="clickable"
        material="opacity: 0; transparent: true"
        position="0 1.2 -1.9"
        scale="0.8 0.8 0.1"
        part-story="name: head; text: A faint dent above my eyebrow from a childhood tree-climbing adventure reminds me that curiosity can leave gentle marks. That oak tree taught me about heights, courage, and the sweet ache of growing up fearlessly."
      ></a-box>

      <a-box id="handArea" class="clickable"
        material="opacity: 0; transparent: true"
        position="-1.2 0.2 -1.9"
        scale="0.6 0.8 0.1"
        part-story="name: hand; text: I once broke my hand falling off a bicycle during a summer evening ride. Seven weeks in plaster taught me patience and respect for the fragility of our bodies. The cast became a canvas for friends' signatures and doodles."
      ></a-box>

      <a-box id="bodyArea" class="clickable"
        material="opacity: 0; transparent: true"
        position="0 0 -1.9"
        scale="1.2 1.5 0.1"
        part-story="name: body; text: My torso carries the invisible weight of every breath, every heartbeat, every moment of joy and sorrow. It's the vessel that houses my dreams, the core that keeps me grounded while my spirit soars through life's adventures."
      ></a-box>

      <a-box id="footArea" class="clickable"
        material="opacity: 0; transparent: true"
        position="0 -1.5 -1.9"
        scale="0.8 0.6 0.1"
        part-story="name: foot; text: My right foot still bears the scar from stepping on a sharp seashell at age ten. The memory of that seaside summer is written in tissue—a reminder of childhood adventures and the stories our bodies carry through time."
      ></a-box>

      <!-- Detail images (initially hidden) -->
      <a-plane id="detailHead"
        material="shader: flat; src: #img-head; side: double; transparent: true; alphaTest: 0.1"
        position="1 0 -1.5"
        scale="1.5 1.5 1"
        visible="false"
      ></a-plane>

      <a-plane id="detailHand"
        material="shader: flat; src: #img-hand; side: double; transparent: true; alphaTest: 0.1"
        position="1 0 -1.5"
        scale="1.5 1.5 1"
        visible="false"
      ></a-plane>

      <a-plane id="detailBody"
        material="shader: flat; src: #img-body; side: double; transparent: true; alphaTest: 0.1"
        position="1 0 -1.5"
        scale="1.5 1.5 1"
        visible="false"
      ></a-plane>

      <a-plane id="detailFoot"
        material="shader: flat; src: #img-foot; side: double; transparent: true; alphaTest: 0.1"
        position="1 0 -1.5"
        scale="1.5 1.5 1"
        visible="false"
      ></a-plane>

      <!-- Lighting -->
      <a-light type="ambient" color="#404040" intensity="0.8"></a-light>
      <a-light type="directional" position="2 4 5" color="#ffffff" intensity="0.5"></a-light>
    </a-scene>

    <script>
      // Register A-Frame component for interactive body parts
      AFRAME.registerComponent('part-story', {
        schema: {
          name: { type: 'string' },
          text: { type: 'string' }
        },
        init() {
          // Click/touch event handlers
          this.el.addEventListener('click', (e) => {
            e.stopPropagation();
            showDetailView(this.el);
          });

          this.el.addEventListener('touchstart', (e) => {
            e.preventDefault();
            e.stopPropagation();
            showDetailView(this.el);
          });
        }
      });

      let isDetailView = false;
      let currentPart = null;

      // Elements
      const storyPanel = document.getElementById('storyPanel');
      const storyTitle = document.getElementById('storyTitle');
      const storyText = document.getElementById('storyText');
      const closeButton = document.getElementById('closeButton');
      const instructions = document.getElementById('instructions');
      const mainBody = document.getElementById('mainBody');

      function showDetailView(clickedElement) {
        if (isDetailView) return;

        isDetailView = true;
        currentPart = clickedElement;

        // Hide instructions
        instructions.classList.add('hidden');

        // Get story data
        const storyData = clickedElement.components['part-story'].data;
        const partName = storyData.name;

        // Show story panel
        storyTitle.textContent = partName;
        storyText.textContent = storyData.text;
        storyPanel.classList.add('active');

        // Animate main body to left side with blur and opacity
        mainBody.setAttribute('animation__position', {
          property: 'position',
          to: '-2 0 -2',
          dur: 800,
          easing: 'easeInOutCubic'
        });

        mainBody.setAttribute('animation__scale', {
          property: 'scale',
          to: '1.2 1.2 1',
          dur: 800,
          easing: 'easeInOutCubic'
        });

        mainBody.setAttribute('animation__opacity', {
          property: 'material.opacity',
          to: 0.5,
          dur: 800,
          easing: 'easeInOutCubic'
        });

        // Show detail image in center
        const detailElement = document.getElementById(`detail${partName.charAt(0).toUpperCase() + partName.slice(1)}`);
        if (detailElement) {
          detailElement.setAttribute('visible', true);
          detailElement.setAttribute('animation__appear', {
            property: 'scale',
            from: '0.1 0.1 0.1',
            to: '1.5 1.5 1',
            dur: 600,
            delay: 200,
            easing: 'easeOutBack'
          });
        }

        // Apply blur effect to main body
        setTimeout(() => {
          mainBody.object3D.traverse((obj) => {
            if (obj.material && obj.material.map) {
              obj.material.map.minFilter = THREE.LinearFilter;
              obj.material.map.magFilter = THREE.LinearFilter;
            }
          });
        }, 100);
      }

      function hideDetailView() {
        if (!isDetailView) return;

        isDetailView = false;

        // Hide story panel
        storyPanel.classList.remove('active');

        // Animate main body back to center
        mainBody.setAttribute('animation__position', {
          property: 'position',
          to: '0 0 -2',
          dur: 800,
          easing: 'easeInOutCubic'
        });

        mainBody.setAttribute('animation__scale', {
          property: 'scale',
          to: '2 2 1',
          dur: 800,
          easing: 'easeInOutCubic'
        });

        mainBody.setAttribute('animation__opacity', {
          property: 'material.opacity',
          to: 1,
          dur: 800,
          easing: 'easeInOutCubic'
        });

        // Hide detail image
        if (currentPart) {
          const storyData = currentPart.components['part-story'].data;
          const partName = storyData.name;
          const detailElement = document.getElementById(`detail${partName.charAt(0).toUpperCase() + partName.slice(1)}`);
          if (detailElement) {
            detailElement.setAttribute('animation__disappear', {
              property: 'scale',
              to: '0.1 0.1 0.1',
              dur: 400,
              easing: 'easeInBack'
            });

            setTimeout(() => {
              detailElement.setAttribute('visible', false);
              detailElement.setAttribute('scale', '1.5 1.5 1');
            }, 400);
          }
        }

        // Remove blur effect
        mainBody.object3D.traverse((obj) => {
          if (obj.material && obj.material.map) {
            obj.material.map.minFilter = THREE.NearestFilter;
            obj.material.map.magFilter = THREE.LinearFilter;
          }
        });

        currentPart = null;
      }

      // Event listeners
      closeButton.addEventListener('click', hideDetailView);

      // Click anywhere to close (except on story panel)
      document.addEventListener('click', (e) => {
        if (isDetailView && !storyPanel.contains(e.target)) {
          hideDetailView();
        }
      });

      // Prevent context menu on long press
      document.addEventListener('contextmenu', (e) => {
        if (e.target.closest('.clickable')) {
          e.preventDefault();
        }
      });

      console.log('Interactive Anatomy Stories loaded');
    </script>
  </body>
</html>
