<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Anatomy Stories</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: Arial, sans-serif; background: #222; color: #fff; overflow: hidden; }

    #stage {
      position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
      width: 60vw; max-width: 800px; aspect-ratio: 3/4;
      transition: transform 0.6s ease; transform-origin: center;
    }
    #stage.zoomed { transform: translate(-50%, -50%) scale(1.8); }

    .layer { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }
    .layer img { width: 100%; height: 100%; object-fit: cover; }

    .hotspot { fill: transparent; stroke: none; cursor: pointer; transition: fill 0.3s; }
    .hotspot:hover { fill: rgba(255,255,255,0.2); }

    #modal {
      position: fixed; top: 0; right: -400px; width: 350px; height: 100vh;
      background: rgba(0,0,0,0.9); padding: 2rem; transition: right 0.5s ease;
      border-left: 2px solid #555; z-index: 100;
    }
    #modal.active { right: 0; }
    #modal h2 { margin-bottom: 1rem; color: #fff; text-transform: capitalize; }
    #modal p { line-height: 1.6; color: #ccc; margin-bottom: 2rem; }
    #modal button {
      background: #555; color: #fff; border: none; padding: 0.5rem 1rem;
      border-radius: 4px; cursor: pointer; transition: background 0.3s;
    }
    #modal button:hover { background: #777; }
  </style>
</head>
<body>
  <div id="stage">
    <div class="layer"><img src="png/body.png" alt="Body"></div>
    <div class="layer"><img src="png/leg.png" alt="Leg"></div>
    <div class="layer"><img src="png/hand.png" alt="Hand"></div>

    <svg class="layer" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
      <rect id="heart" class="hotspot" x="160" y="120" width="80" height="60"/>
      <rect id="elbow" class="hotspot" x="50" y="200" width="60" height="40"/>
      <rect id="knee" class="hotspot" x="180" y="400" width="60" height="40"/>
      <rect id="hand" class="hotspot" x="300" y="180" width="50" height="80"/>
    </svg>
  </div>

  <div id="modal">
    <h2 id="story-title">Body Part</h2>
    <p id="story-text">Story text will appear here...</p>
    <button onclick="closeModal()">Close</button>
  </div>

  <script>
    const stories = {
      heart: "The heart beats 100,000 times a day, pumping life through every vessel. It remembers every emotion, every moment of joy and sorrow.",
      elbow: "This joint has bent a million times, helping hands reach dreams. A childhood scar reminds me of learning to ride a bike.",
      knee: "Scraped countless times in youth, this knee carried me through adventures. It bears the wisdom of every fall and rise.",
      hand: "These fingers have written love letters, wiped tears, and created art. Each line tells a story of touch and connection."
    };

    let isZoomed = false;
    const stage = document.getElementById('stage');
    const modal = document.getElementById('modal');
    const title = document.getElementById('story-title');
    const text = document.getElementById('story-text');

    document.querySelectorAll('.hotspot').forEach(hotspot => {
      hotspot.addEventListener('click', (e) => {
        const id = e.target.id;
        if (isZoomed) {
          showStory(id);
        } else {
          stage.classList.add('zoomed');
          isZoomed = true;
          setTimeout(() => showStory(id), 300);
        }
      });
    });

    function showStory(id) {
      title.textContent = id.charAt(0).toUpperCase() + id.slice(1);
      text.textContent = stories[id] || "No story available.";
      modal.classList.add('active');
    }

    function closeModal() {
      modal.classList.remove('active');
      stage.classList.remove('zoomed');
      isZoomed = false;
    }

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') closeModal();
    });
  </script>
</body>
</html>
