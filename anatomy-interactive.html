<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Anatomy</title>

  <!-- External stylesheets -->
  <link rel="stylesheet" href="styles.css">


</head>
<body>
  <!--
  ========================================
  STUDENT CONFIGURATION SECTION
  ========================================
  Edit the values below to customize your anatomy interface.
  Do NOT edit anything below the "END OF CONFIGURATION" line.
  -->

  <script id="config">
    const CONFIG = {
      // Main body image settings
      body: {
        src: "png/body.png",
        onclick: "", // Optional: image to show when body is clicked (leave empty for zoom)
        background: "png/background.png"
      },

      // Hotspot button settings
      buttons: {
        "button-1": {
          src: "svg/hand.svg",
          lineColor: "#ffffff",
          lineThickness: "2px",
          fillColor: "#ffffff",
          fillOpacity: "20%"
        },
        "button-2": {
          src: "svg/leg.svg",
          lineColor: "#ffffff",
          lineThickness: "2px",
          fillColor: "#ffffff",
          fillOpacity: "20%"
        }
      },

      // Text styling settings
      text: {
        textSize: "12pt",
        font: "", // Optional: link to custom font
        textColor: "#cccccc",
        background: "", // Optional: background image
        backgroundColor: "#000000",
        backgroundOpacity: 0.8
      },

      // Zoomed text styling settings
      scaledText: {
        textSize: "16pt",
        // font is inherited from text.font
        textColor: "#cccccc",
        background: "", // Optional: background image
        backgroundColor: "#000000",
        backgroundOpacity: 1
      },

      // Body part detail settings
      bodyparts: {
        "bodypart-1": {
          src: "png/leg.png",
          scale: 1,
          onclick: "", // Optional: different image on click
          title: "Leg Muscles",
          text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Proin eget tortor risus. Vestibulum ac diam sit amet quam vehicula elementum sed sit amet dui. Mauris blandit aliquet elit, eget tincidunt nibh pulvinar a. Curabitur non nulla sit amet nisl tempus convallis quis ac lectus. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas. Vestibulum tortor quam, feugiat vitae, ultricies eget, tempor sit amet, ante. Donec eu libero sit amet quam egestas semper. Aenean ultricies mi vitae est. Mauris placerat eleifend leo. Quisque velit nisi, pretium ut lacinia in, elementum id enim."
        },
        "bodypart-2": {
          src: "png/hand.png",
          scale: 1,
          onclick: "",
          title: "Hand Anatomy",
          text: "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum."
        }
      },

      // Back button settings
      backButton: {
        image: "", // Optional: custom back button image
        scale: 1
      },

      // Connection between buttons and bodyparts
      connections: {
        "button-1": "bodypart-1", // hand button shows hand bodypart
        "button-2": "bodypart-2"  // leg button shows leg bodypart
      }
    };
  </script>

  <!-- ========================================
       END OF CONFIGURATION - DO NOT EDIT BELOW
       ======================================== -->

  <div id="stage">
    <!-- Background layer -->
    <div id="background-layer" class="layer">
      <img src="" alt="Background">
    </div>

    <!-- Main body layer -->
    <div id="body-layer" class="layer">
      <img src="png/body.png" alt="Body">
    </div>

    <!-- Detail layers (initially hidden) -->
    <div id="bodypart-1-detail" class="layer detail-layer">
      <img src="" alt="Body Part Detail">
    </div>
    <div id="bodypart-2-detail" class="layer detail-layer">
      <img src="" alt="Body Part Detail">
    </div>

    <!-- SVG overlay with precise hotspots -->
    <div class="layer">
      <!-- Button 1 SVG hotspot -->
      <svg id="button-1-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-button="button-1">
        <path class="hotspot-path" d="M374.21,212.19c2.36,9.92,12.26,18.04,12.26,18.04l24.17-9.02,31.66-17.53s2.38-11.91-6.13-20.77-10.55-7.49-10.55-7.49l-26.89,15.32-10.89,6.13-13.62-1.36s-1.7,9.53,0,16.68Z"/>
      </svg>

      <!-- Button 2 SVG hotspot -->
      <svg id="button-2-hotspot" class="hotspot-svg" viewBox="0 0 600 853" xmlns="http://www.w3.org/2000/svg" data-button="button-2">
        <path class="hotspot-path" d="M302.72,426.5s-1.36,25.35-2.72,41.69-6.13,36.09-4.43,46.3,7.49,8.17,10.55,9.87,34.04,1.7,37.79,0,18.38-48.29,18.38-48.29c0,0,13.62-46.32,13.62-49.57s-5.11-6.65-22.13-4.27-51.06,4.27-51.06,4.27Z"/>
      </svg>
    </div>
  </div>

  <!-- Text panel -->
  <div id="text-panel">
    <h2 id="story-title">Body Part</h2>
    <p id="story-text">Story text will appear here...</p>
  </div>

  <!-- Back button for zoom view -->
  <button class="back-button" id="back-button">←</button>

  <!-- Text overlay for zoomed text -->
  <div class="text-overlay" id="text-overlay">
    <h2 id="overlay-title">Body Part</h2>
    <p id="overlay-text">Story text will appear here...</p>
  </div>

  <!-- Import the main script -->
  <script src="script.js"></script>


</body>
</html>
