<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interactive Anatomy Stories</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body { font-family: Arial, sans-serif; background: #222; color: #fff; overflow: hidden; }

    #stage {
      position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);
      width: 60vw; max-width: 800px; aspect-ratio: 3/4;
    }

    .layer { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

    #body-layer {
      transition: all 0.8s ease;
      filter: blur(0px);
    }
    #body-layer.blurred {
      transform: translateX(-33%) scale(0.8);
      filter: blur(3px);
      opacity: 0.5;
    }
    #body-layer img { width: 100%; height: 100%; object-fit: cover; }

    .detail-layer {
      opacity: 0;
      transform: scale(0);
      transition: all 0.8s ease;
      z-index: 10;
    }
    .detail-layer.active {
      opacity: 1;
      transform: scale(0.8);
    }
    .detail-layer img { width: 100%; height: 100%; object-fit: cover; }

    .hotspot {
      fill: transparent;
      stroke: #fff;
      stroke-width: 2;
      cursor: pointer;
      transition: all 0.3s;
      opacity: 0.7;
    }
    .hotspot:hover {
      fill: rgba(255,255,255,0.2);
      stroke-width: 3;
      opacity: 1;
    }

    #text-panel {
      position: fixed;
      top: 50%;
      right: 5%;
      width: 300px;
      transform: translateY(-50%) scale(0);
      opacity: 0;
      transition: all 0.8s ease;
      background: rgba(0,0,0,0.8);
      padding: 2rem;
      border-radius: 10px;
      border: 1px solid #555;
      z-index: 20;
    }
    #text-panel.active {
      transform: translateY(-50%) scale(1);
      opacity: 1;
    }
    #text-panel h2 {
      margin-bottom: 1rem;
      color: #fff;
      text-transform: capitalize;
      font-size: 1.5rem;
    }
    #text-panel p {
      line-height: 1.6;
      color: #ccc;
      font-size: 0.9rem;
    }
  </style>
</head>
<body>
  <div id="stage">
    <!-- Main body layer -->
    <div id="body-layer" class="layer">
      <img src="png/body.png" alt="Body">
    </div>

    <!-- Detail layers (initially hidden) -->
    <div id="leg-detail" class="layer detail-layer">
      <img src="png/leg.png" alt="Leg Detail">
    </div>
    <div id="hand-detail" class="layer detail-layer">
      <img src="png/hand.png" alt="Hand Detail">
    </div>

    <!-- SVG overlay with visible hotspots -->
    <svg class="layer" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
      <rect id="heart" class="hotspot" x="160" y="120" width="80" height="60" data-detail="body"/>
      <rect id="elbow" class="hotspot" x="50" y="200" width="60" height="40" data-detail="hand"/>
      <rect id="knee" class="hotspot" x="180" y="400" width="60" height="40" data-detail="leg"/>
      <rect id="hand" class="hotspot" x="300" y="180" width="50" height="80" data-detail="hand"/>
    </svg>
  </div>

  <!-- Text panel -->
  <div id="text-panel">
    <h2 id="story-title">Body Part</h2>
    <p id="story-text">Story text will appear here...</p>
  </div>

  <script>
    const stories = {
      heart: "The heart beats 100,000 times a day, pumping life through every vessel. It remembers every emotion, every moment of joy and sorrow.",
      elbow: "This joint has bent a million times, helping hands reach dreams. A childhood scar reminds me of learning to ride a bike.",
      knee: "Scraped countless times in youth, this knee carried me through adventures. It bears the wisdom of every fall and rise.",
      hand: "These fingers have written love letters, wiped tears, and created art. Each line tells a story of touch and connection."
    };

    let isZoomed = false;
    const stage = document.getElementById('stage');
    const modal = document.getElementById('modal');
    const title = document.getElementById('story-title');
    const text = document.getElementById('story-text');

    document.querySelectorAll('.hotspot').forEach(hotspot => {
      hotspot.addEventListener('click', (e) => {
        const id = e.target.id;
        if (isZoomed) {
          showStory(id);
        } else {
          stage.classList.add('zoomed');
          isZoomed = true;
          setTimeout(() => showStory(id), 300);
        }
      });
    });

    function showStory(id) {
      title.textContent = id.charAt(0).toUpperCase() + id.slice(1);
      text.textContent = stories[id] || "No story available.";
      modal.classList.add('active');
    }

    function closeModal() {
      modal.classList.remove('active');
      stage.classList.remove('zoomed');
      isZoomed = false;
    }

    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape') closeModal();
    });
  </script>
</body>
</html>
